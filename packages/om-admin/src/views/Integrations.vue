<template lang="pug">
div
  .brand-wrapper.re-integration
    om-heading(h1) {{ getTitle }}
    .wrapper
      .alert-block-re-integration(v-if="!isAllReIntegrated")
        alert(type="warning" :integrationType="getIntegrationType")
      .deprecated-integrations
        DeprecatedIntegrations(
          :integrationType="getIntegrationType"
          :filteredIntegrations="filteredIntegrations"
          :campaignsByStatus="campaignsByStatus"
        )
      template(v-if="!needReIntegration")
        .integrations-accordion.col-md-8.mt-4(v-if="integrationsByType.length > 0")
          accordion
            accordion-item(
              v-for="(integration, index) in integrationsByType"
              :key="integration._id"
              :step="null"
              :open-default="index === 0"
            )
              template(#name)
                .d-flex.justify-content-between.align-items-center.w-100
                  span {{ integration.data.name }}
              .integration-type-content
                IntegrationTypeComponent(
                  :integration-type="getIntegrationType"
                  :integration="integration"
                  :campaigns-by-status="campaignsByStatus"
                )
</template>
<script>
  import { capitalizeFirstLetter } from '@/util';
  import { mapGetters } from 'vuex';
  import { getConfigForIntegration } from '@om/integrations';
  import reIntegrationMixin from '@/components/ReIntegration/reIntegration';
  import Accordion, { AccordionItem } from '@/components/Elements/Accordion';

  export default {
    components: {
      Alert: () => import('@/components/ReIntegration/Alerts/Alert.vue'),
      Success: () => import('@/components/ReIntegration/Alerts/Success.vue'),
      DeprecatedIntegrations: () => import('@/components/ReIntegration/DeprecatedIntegrations.vue'),
      Accordion,
      AccordionItem,
      IntegrationTypeComponent: () => import('@/components/IntegrationTypeComponent.vue'),
    },
    mixins: [reIntegrationMixin],
    data() {
      return {
        integrationsByType: [],
        campaignsByStatus: {},
      };
    },
    computed: {
      ...mapGetters(['integrations']),
      getIntegrationType() {
        return this.$route.query.integrationType;
      },
      filteredIntegrations() {
        const query = this.$route.query;

        // If no query parameters, return all integrations
        if (!Object.keys(query).length) {
          return this.integrations;
        }

        return this.integrations.filter((integration) => {
          // Filter by integration type (backward compatibility)
          if (query.integrationType && integration.type !== query.integrationType) {
            return false;
          }

          // Filter by integration types (comma-separated list)
          if (query.integrationTypes) {
            const types = query.integrationTypes.split(',').map((t) => t.trim());
            if (!types.includes(integration.type)) {
              return false;
            }
          }

          return true;
        });
      },
      isDeprecated() {
        return getConfigForIntegration(this.getIntegrationType).deprecated || false;
      },
      needReIntegration() {
        return this.filteredIntegrations.length > 0 && this.isDeprecated;
      },
      getTitle() {
        let integration = this.getIntegrationType;
        if (integration === 'klaviyoOAuth') {
          integration = 'klaviyo';
        }
        return this.$t('integrationsPage.title', {
          integrationType: capitalizeFirstLetter(integration),
        });
      },
    },
    async mounted() {
      this.integrationsByType = this.integrations.filter((integration) => {
        return integration.type === this.getIntegrationType;
      });

      if (this.filteredIntegrations?.length) {
        const integrationIds = this.filteredIntegrations.map((i) => i._id);
        const campaigns = await this.getCampaignsByIntegrationId(integrationIds);
        this.campaignsByStatus = campaigns;
      }
    },
    methods: {
      capitalizeFirstLetter,
    },
  };
</script>
<style lang="sass">
  @import '@/components/ReIntegration/reIntegration.sass'
</style>
