const moment = require('moment');
const { PERIODS: ShopifyPeriods } = require('../enums');

const findRecurringPlan = (charge) => {
  return charge.lineItems.find((item) => {
    const detail = item.plan.pricingDetails;
    if (!detail.interval) return false;
    return true;
  });
};

const findUsagePlan = (charge) => {
  return charge.lineItems.find((item) => {
    const detail = item.plan.pricingDetails;
    if (detail.interval) return false;
    if (!detail.cappedAmount) return false;
    return true;
  });
};

const getIdFromGid = (gid) => {
  const regExpResult = gid.match(new RegExp(/^.*\/([0-9].*)$/, 'i'));
  return regExpResult ? parseInt(regExpResult[1], 10) : null;
};

const _formatRecurringPlan = (recurringPlan) => {
  const result = recurringPlan.plan.pricingDetails;

  result.price.amount = parseFloat(result.price.amount);
  if (result.price.currencyCode === 'USD') {
    result.price.amountInHuf = result.price.amount * 250;
  } else {
    result.price.amountInHuf = result.price.amount;
  }

  if (result.interval === ShopifyPeriods.MONTHLY) {
    result.intervalInMonth = 1;
  } else if (result.interval === ShopifyPeriods.ANNUAL) {
    result.intervalInMonth = 12;
  } else {
    result.intervalInMonth = null;
  }

  return result;
};

const _formatUsagePlan = (usagePlan) => {
  const result = usagePlan.plan.pricingDetails;

  result.cappedAmount.amount = parseFloat(result.cappedAmount.amount);
  if (result.cappedAmount.currencyCode === 'USD') {
    result.cappedAmount.amountInHuf = parseFloat(result.cappedAmount.amount * 250, 10);
  } else {
    result.cappedAmount.amountInHuf = result.cappedAmount.amount;
  }

  result.balanceUsed.amount = parseFloat(result.balanceUsed.amount);
  if (result.balanceUsed.currencyCode === 'USD') {
    result.balanceUsed.amountInHuf = parseFloat(result.balanceUsed.amount * 250, 10);
  } else {
    result.balanceUsed.amountInHuf = result.balanceUsed.amount;
  }
  return result;
};

const _formatDate = (date, logger) => {
  let result = null;
  try {
    const momentDate = moment(date);
    if (momentDate.isValid()) {
      result = momentDate;
    }
  } catch (e) {
    logger.error({
      type: 'formatter',
      accountId: this.accountId,
      shopName: this.shopName,
      formatterType: 'formatSubscriptionData.formatDate',
      caughtError: e.message,
      errorData: null,
    });
  }
  return result;
};

/* sample result
{
  id: ***********,
  gid: 'gid://shopify/AppSubscription/***********',
  status: 'ACTIVE',
  test: true,
  name: 'SILVER WITH FLEXIPAY',
  createdAt: Moment<2022-11-24T14:27:08+00:00>,
  currentPeriodEnd: Moment<2022-11-24T14:27:08+00:00>,
  recurringPlan: {
    interval: 'EVERY_30_DAYS',
    intervalInMonth: 1,
    price: { amount: 67.96, currencyCode: 'USD', amountInHuf: 16990 },
    discount: null,
  },
  usagePlan: {
    terms: 'Overcharge after every 10k pageviews',
    cappedAmount: { amount: 67.96, currencyCode: 'USD', amountInHuf: 16990 },
    balanceUsed: { amount: 0.0, currencyCode: 'USD', amountInHuf: 0.0 },
  },
}
*/
const formatSubscriptionData = (charge, logger) => {
  const result = JSON.parse(JSON.stringify(charge));
  result.gid = result.id;

  result.id = getIdFromGid(result.gid);

  const recurringPlan = findRecurringPlan(result);
  result.recurringPlan = recurringPlan ? _formatRecurringPlan(recurringPlan) : null;

  const usagePlan = findUsagePlan(result);
  result.usagePlan = usagePlan ? _formatUsagePlan(usagePlan) : null;

  delete result.lineItems;

  result.createdAt = _formatDate(result.createdAt, logger);
  result.currentPeriodEnd = _formatDate(result.currentPeriodEnd, logger);

  result.status = result.status.toLowerCase();

  return result;
};

module.exports = { formatSubscriptionData, findRecurringPlan, findUsagePlan, getIdFromGid };
