import {
  ConditionalExpression,
  OPERATORS,
} from '@jetfabric/schemas/lib/protocol-sdk-api-v2/query/condition';
import { isLeft, isRight } from '../../lib/either';
import { evaluate } from '../../lib/main';
import { mkDataReference, trivialQueryResolver } from './testbase';

describe('LET expressions', () => {
  it('Simple let expression with variable reference', async () => {
    const expression: ConditionalExpression = {
      bindings: {
        x: 2,
      },
      body: [OPERATORS.LT, { variable: 'x' }, 3],
    };
    const result = await evaluate(expression, trivialQueryResolver());
    expect(isRight(result)).toBe(true);
    if (isRight(result)) {
      expect(result.value as any).toBe(true);
    }
  });

  it('Nested LETs', async () => {
    const expression: ConditionalExpression = {
      bindings: {
        x: 2,
      },
      body: {
        bindings: {
          y: 2,
        },
        body: [OPERATORS.EQ, { variable: 'x' }, { variable: 'y' }],
      },
    };
    const result = await evaluate(expression, trivialQueryResolver());
    expect(isRight(result)).toBe(true);
    if (isRight(result)) {
      expect(result.value as any).toBe(true);
    }
  });

  it('overridden variable bindings', async () => {
    const expression: ConditionalExpression = {
      bindings: {
        x: 2,
      },
      body: {
        bindings: {
          x: 4,
        },
        body: [OPERATORS.LT, 3, { variable: 'x' }],
      },
    };
    const result = await evaluate(expression, trivialQueryResolver());
    expect(isRight(result)).toBe(true);
    if (isRight(result)) {
      expect(result.value as any).toBe(true);
    }
  });

  it('resolvable data reference as variable binding value', async () => {
    const expression: ConditionalExpression = {
      bindings: {
        x: mkDataReference('q1'),
      },
      body: [OPERATORS.GT, { variable: 'x' }, 3],
    };
    const result = await evaluate(expression, trivialQueryResolver(4));
    expect(isRight(result)).toBe(true);
    if (isRight(result)) {
      expect(result.value as any).toBe(true);
    }
  });

  it('unresolvable data reference as variable binding value', async () => {
    const expression: ConditionalExpression = {
      bindings: {
        x: mkDataReference('q1'),
      },
      body: [OPERATORS.GT, { variable: 'x' }, 3],
    };
    const result = await evaluate(expression, trivialQueryResolver());
    expect(isRight(result)).toBe(false);
    expect(result.value as any).toEqual(expression);
  });

  it('partially resolved bindings become partially resolved', async () => {
    const expression: ConditionalExpression = {
      bindings: {
        x: [OPERATORS.SUM, 1, 2, 3, mkDataReference('q1')],
      },
      body: [OPERATORS.GT, { variable: 'x' }, 3],
    };
    const result = await evaluate(expression, trivialQueryResolver());
    expect(isRight(result)).toBe(false);
    expect(result.value as any).toEqual({
      bindings: {
        x: [OPERATORS.SUM, 6, mkDataReference('q1')],
      },
      body: [OPERATORS.GT, { variable: 'x' }, 3],
    });
  });

  it('unresolved nested bindings', async () => {
    const expression: ConditionalExpression = {
      bindings: {
        x: mkDataReference('q1'),
      },
      body: {
        bindings: {
          y: mkDataReference('q2'),
        },
        body: [OPERATORS.SUM, { variable: 'x' }, { variable: 'y' }],
      },
    };
    const result = await evaluate(expression, trivialQueryResolver());
    expect(isRight(result)).toBe(false);
    expect(result.value as any).toEqual(expression);
  });

  it('partially resolved nested bindings', async () => {
    const expression: ConditionalExpression = {
      bindings: {
        x: mkDataReference('q1'),
        y: mkDataReference('q2'),
      },
      body: {
        bindings: {
          z: mkDataReference('q3'),
        },
        body: [OPERATORS.SUM, { variable: 'x' }, { variable: 'y' }, { variable: 'z' }],
      },
    };
    const result = await evaluate(expression, trivialQueryResolver(1));
    expect(isRight(result)).toBe(false);
    expect(result.value as any).toEqual({
      bindings: {
        x: 1,
        y: mkDataReference('q2'),
      },
      body: {
        bindings: {
          z: mkDataReference('q3'),
        },
        body: [OPERATORS.SUM, 1, { variable: 'y' }, { variable: 'z' }],
      },
    });
  });
});
